<script setup lang="ts">
/**
 * 官方文档
 * @see https://jessibuca.com/api.html
 *
 */
import { MonitorAPI } from '@/api/monitor/monitor.ts'

const { videoUrl, loading = false, deviceId, channelId, live = true } = defineProps<{
  loading?: boolean
  videoUrl?: string
  deviceId: string
  channelId: string
  // 是否是直播，回放的话不需要云台控制按钮
  live?: boolean
}>()
const emit = defineEmits<{ (e: 'timeUpdate', delta: number) }>()

let player: Jessibuca = null

let preTimestamp = 0
function timeUpdate(e: number) {
  const delta = e - preTimestamp
  if (delta < 500) {
    emit('timeUpdate', e - preTimestamp)
  }

  preTimestamp = e
}

let offWatch = () => {}

const videoContainerRef = useTemplateRef('videoContainerRef')

const showSelfButton = ref(false)
onMounted(() => {
  player = new Jessibuca({
    container: videoContainerRef.value,
    videoBuffer: 0.1, // 缓存时长
    isResize: false,
    supportDblclickFullscreen: true,
    keepScreenOn: true, // 开启屏幕常亮，在手机浏览器上, canvas标签渲染视频并不会像video标签那样保持屏幕常亮
    hotKey: true, // 否开启键盘快捷键
    controlAutoHide: true, // 底部控制台是否自动隐藏
    useWebFullScreen: true, // 是否使用web全屏(旋转90度)（只会在移动端生效）
    loadingText: '加载中...',
    decoder: '/jessibuca/decoder.js',
    useMSE: true,
    debug: false,
    showBandwidth: false, // 显示网速
    operateBtns: {
      fullscreen: true,
      screenshot: true,
      play: true,
      audio: true,
    },
    isNotMute: false,
    useWCS: true,
    autoWasm: true,
  })

  player.on('timeUpdate', timeUpdate)

  /** 创建一个位于图标集合最前列位置的元素，方便将自定义图标放在最前面，默认会放在最后面 */
  const div = document.createElement('div')
  div.classList.add('jessibuca-controls-right-first', 'flex')
  const aimEl = document.querySelector('.jessibuca-controls-right')
  aimEl.insertBefore(div, aimEl.firstChild)

  offWatch = watch(
    () => videoUrl,
    (url) => {
      if (url) {
        player.play(videoUrl)
      }
    },
    { immediate: true },
  )

  showSelfButton.value = true
})

const holderShow = ref(false)

// 云台控制相关状态
function togglePtzControl() {
  holderShow.value = !holderShow.value
}

function closePtzControl() {
  holderShow.value = false
}

/** S 云台控制功能 */
/**
 * 操控云台
 */
function control(direction: string) {
  MonitorAPI.ptzControl(deviceId, channelId, direction).then((res) => {
    console.log('云台控制', res)
  })
}
/** E 云台控制功能 */

defineExpose({
  closePtzControl,
})

onUnmounted(() => {
  offWatch()

  if (player) {
    player.destroy().finally(() => {
      player = null
    })
  }
})
</script>

<template>
  <div class="liveVideo">
    <div ref="videoContainerRef" v-loading="loading" class="bg-black" />
  </div>
</template>

<style lang="scss" scoped></style>
